const fs = require('fs-extra');
const path = require('path');
const { createWorker } = require('tesseract.js');
const sharp = require('sharp');
const winston = require('winston');
const config = require('config');
const { v4: uuidv4 } = require('uuid');

class OCRService {
  constructor() {
    this.logger = winston.createLogger({
      level: config.get('logging.level'),
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: config.get('logging.file.filename') })
      ]
    });
    
    this.workers = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize OCR workers
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      this.logger.info('Initializing OCR service...');
      
      const languages = config.get('ocr.languages');
      const langString = languages.join('+');
      
      // Create worker for Arabic + English
      const worker = await createWorker(langString, 1, {
        logger: m => {
          if (m.status === 'recognizing text') {
            this.logger.debug(`OCR Progress: ${Math.round(m.progress * 100)}%`);
          }
        }
      });

      await worker.setParameters({
        tessedit_pageseg_mode: config.get('ocr.psm')[0],
        tessedit_ocr_engine_mode: config.get('ocr.oem')[0],
        tessedit_char_whitelist: '',
        preserve_interword_spaces: '1'
      });

      this.workers.set('primary', worker);
      this.isInitialized = true;
      
      this.logger.info('OCR service initialized successfully');
    } catch (error) {
      this.logger.error(`OCR initialization failed: ${error.message}`);
      throw new Error(`Failed to initialize OCR: ${error.message}`);
    }
  }

  /**
   * Process PDF pages with OCR
   * @param {Array} imagePages - Array of image buffers from PDF pages
   * @param {Object} options - OCR options
   * @returns {Promise<Array>} OCR results for each page
   */
  async processPages(imagePages, options = {}) {
    await this.initialize();

    try {
      this.logger.info(`Starting OCR processing for ${imagePages.length} pages`);
      
      const results = [];
      const lang = options.lang || config.get('ocr.defaultLang');
      
      for (let i = 0; i < imagePages.length; i++) {
        this.logger.info(`Processing page ${i + 1}/${imagePages.length}`);
        
        const pageResult = await this.processPage(imagePages[i], {
          ...options,
          pageNumber: i + 1,
          lang
        });
        
        results.push(pageResult);
      }

      this.logger.info(`OCR processing completed for ${imagePages.length} pages`);
      return results;

    } catch (error) {
      this.logger.error(`OCR processing failed: ${error.message}`);
      throw new Error(`OCR processing failed: ${error.message}`);
    }
  }

  /**
   * Process a single page with OCR
   * @param {Buffer} imageBuffer - Image buffer
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} OCR result
   */
  async processPage(imageBuffer, options = {}) {
    try {
      const pageNumber = options.pageNumber || 1;
      const lang = options.lang || config.get('ocr.defaultLang');
      
      // Preprocess image
      const preprocessedImage = await this.preprocessImage(imageBuffer, options);
      
      // Try multiple OCR strategies
      const strategies = config.get('ocr.fallbackStrategies');
      let bestResult = null;
      let bestConfidence = 0;

      for (const strategy of strategies) {
        try {
          const result = await this.performOCR(preprocessedImage, {
            ...options,
            ...strategy,
            lang
          });

          if (result.confidence > bestConfidence) {
            bestResult = result;
            bestConfidence = result.confidence;
          }

          // If we get good confidence, use this result
          if (result.confidence >= 0.8) {
            break;
          }
        } catch (strategyError) {
          this.logger.warn(`OCR strategy failed: ${strategyError.message}`);
        }
      }

      if (!bestResult) {
        throw new Error('All OCR strategies failed');
      }

      // Post-process result
      const processedResult = await this.postProcessResult(bestResult, options);
      
      return {
        pageNumber,
        success: true,
        confidence: processedResult.confidence,
        text: processedResult.text,
        words: processedResult.words,
        lines: processedResult.lines,
        paragraphs: processedResult.paragraphs,
        language: this.detectLanguage(processedResult.text),
        processingTime: processedResult.processingTime,
        strategy: bestResult.strategy
      };

    } catch (error) {
      this.logger.error(`Page OCR failed: ${error.message}`);
      return {
        pageNumber: options.pageNumber || 1,
        success: false,
        error: error.message,
        text: '',
        confidence: 0
      };
    }
  }

  /**
   * Preprocess image for better OCR results
   */
  async preprocessImage(imageBuffer, options = {}) {
    try {
      const preprocessing = config.get('ocr.preprocessing');
      const dpi = options.dpi || config.get('ocr.dpi');
      
      let image = sharp(imageBuffer);
      
      // Get image metadata
      const metadata = await image.metadata();
      this.logger.debug(`Original image: ${metadata.width}x${metadata.height}, density: ${metadata.density}`);

      // Resize if needed to achieve target DPI
      if (metadata.density && metadata.density < dpi) {
        const scale = dpi / metadata.density;
        image = image.resize(
          Math.round(metadata.width * scale),
          Math.round(metadata.height * scale)
        );
      }

      // Convert to grayscale
      image = image.greyscale();

      // Apply preprocessing steps
      if (preprocessing.denoise) {
        image = image.median(3); // Noise reduction
      }

      if (preprocessing.sharpen) {
        image = image.sharpen();
      }

      if (preprocessing.adaptiveThreshold) {
        // Enhance contrast
        image = image.normalize();
      }

      // Convert to PNG for OCR
      const processedBuffer = await image.png().toBuffer();
      
      this.logger.debug('Image preprocessing completed');
      return processedBuffer;

    } catch (error) {
      this.logger.warn(`Image preprocessing failed: ${error.message}`);
      return imageBuffer; // Return original if preprocessing fails
    }
  }

  /**
   * Perform OCR with specific parameters
   */
  async performOCR(imageBuffer, options = {}) {
    const startTime = Date.now();
    
    try {
      const worker = this.workers.get('primary');
      
      // Set parameters for this recognition
      await worker.setParameters({
        tessedit_pageseg_mode: options.psm || config.get('ocr.psm')[0],
        tessedit_ocr_engine_mode: options.oem || config.get('ocr.oem')[0]
      });

      // Perform recognition
      const result = await worker.recognize(imageBuffer);
      
      const processingTime = Date.now() - startTime;
      
      return {
        text: result.data.text,
        confidence: result.data.confidence / 100, // Convert to 0-1 scale
        words: this.extractWords(result.data),
        lines: this.extractLines(result.data),
        paragraphs: this.extractParagraphs(result.data),
        processingTime,
        strategy: { psm: options.psm, oem: options.oem, dpi: options.dpi }
      };

    } catch (error) {
      throw new Error(`OCR recognition failed: ${error.message}`);
    }
  }

  /**
   * Extract word-level information
   */
  extractWords(ocrData) {
    if (!ocrData.words) return [];

    return ocrData.words.map(word => ({
      text: word.text,
      confidence: word.confidence / 100,
      bbox: word.bbox,
      baseline: word.baseline,
      language: this.detectLanguage(word.text)
    }));
  }

  /**
   * Extract line-level information
   */
  extractLines(ocrData) {
    if (!ocrData.lines) return [];

    return ocrData.lines.map(line => ({
      text: line.text,
      confidence: line.confidence / 100,
      bbox: line.bbox,
      baseline: line.baseline,
      words: line.words ? line.words.map(word => ({
        text: word.text,
        confidence: word.confidence / 100,
        bbox: word.bbox
      })) : []
    }));
  }

  /**
   * Extract paragraph-level information
   */
  extractParagraphs(ocrData) {
    if (!ocrData.paragraphs) return [];

    return ocrData.paragraphs.map(para => ({
      text: para.text,
      confidence: para.confidence / 100,
      bbox: para.bbox,
      lines: para.lines ? para.lines.map(line => ({
        text: line.text,
        confidence: line.confidence / 100,
        bbox: line.bbox
      })) : []
    }));
  }

  /**
   * Post-process OCR results
   */
  async postProcessResult(result, options = {}) {
    try {
      // Clean up text
      let cleanedText = this.cleanText(result.text);
      
      // Apply language-specific corrections
      cleanedText = this.applyLanguageCorrections(cleanedText);
      
      // Enhance confidence based on text quality
      const enhancedConfidence = this.calculateEnhancedConfidence(
        result.confidence,
        cleanedText,
        result.words
      );

      return {
        ...result,
        text: cleanedText,
        confidence: enhancedConfidence
      };

    } catch (error) {
      this.logger.warn(`Post-processing failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Clean extracted text
   */
  cleanText(text) {
    if (!text) return '';

    // Remove excessive whitespace
    let cleaned = text.replace(/\s+/g, ' ');
    
    // Fix common OCR errors
    cleaned = cleaned.replace(/[|]/g, 'l'); // Pipe to lowercase L
    cleaned = cleaned.replace(/[0]/g, 'O'); // Zero to O in text context
    
    // Clean up Arabic text
    cleaned = this.cleanArabicText(cleaned);
    
    return cleaned.trim();
  }

  /**
   * Clean Arabic text specifically
   */
  cleanArabicText(text) {
    // Remove or fix common Arabic OCR errors
    let cleaned = text;
    
    // Fix reversed parentheses in RTL context
    cleaned = cleaned.replace(/\(/g, '___TEMP_OPEN___');
    cleaned = cleaned.replace(/\)/g, '(');
    cleaned = cleaned.replace(/___TEMP_OPEN___/g, ')');
    
    // Fix common Arabic character confusions
    const arabicCorrections = {
      'ة': 'ه', // Sometimes confused
      'ي': 'ى', // Ya variations
      'ك': 'ک'  // Kaf variations
    };
    
    // Apply corrections cautiously
    Object.entries(arabicCorrections).forEach(([wrong, correct]) => {
      // Only apply if it makes sense in context
      cleaned = cleaned.replace(new RegExp(wrong, 'g'), correct);
    });
    
    return cleaned;
  }

  /**
   * Apply language-specific corrections
   */
  applyLanguageCorrections(text) {
    // English corrections
    text = text.replace(/\bl\b/g, 'I'); // Standalone l to I
    text = text.replace(/\b0\b/g, 'O'); // Standalone 0 to O
    
    // Arabic corrections
    text = this.fixArabicWordBoundaries(text);
    
    return text;
  }

  /**
   * Fix Arabic word boundaries
   */
  fixArabicWordBoundaries(text) {
    // Ensure proper spacing around Arabic text
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F]/;
    const words = text.split(/\s+/);
    
    return words.map(word => {
      if (arabicPattern.test(word)) {
        // Ensure Arabic words are properly separated
        return word.replace(/([a-zA-Z])[\u0600-\u06FF]/g, '$1 ');
      }
      return word;
    }).join(' ');
  }

  /**
   * Calculate enhanced confidence score
   */
  calculateEnhancedConfidence(baseConfidence, text, words = []) {
    let confidence = baseConfidence;
    
    // Boost confidence for longer, coherent text
    if (text.length > 100) {
      confidence *= 1.1;
    }
    
    // Boost confidence if word-level confidence is consistent
    if (words.length > 0) {
      const avgWordConfidence = words.reduce((sum, word) => sum + word.confidence, 0) / words.length;
      if (avgWordConfidence > 0.8) {
        confidence *= 1.05;
      }
    }
    
    // Penalize if text has too many special characters (likely OCR errors)
    const specialCharRatio = (text.match(/[^\w\s\u0600-\u06FF\u0750-\u077F]/g) || []).length / text.length;
    if (specialCharRatio > 0.1) {
      confidence *= 0.9;
    }
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Detect language of text
   */
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F]/;
    const englishPattern = /[a-zA-Z]/;
    
    const arabicMatches = (text.match(arabicPattern) || []).length;
    const englishMatches = (text.match(englishPattern) || []).length;
    const totalChars = text.replace(/\s/g, '').length;
    
    if (totalChars === 0) return 'unknown';
    
    const arabicRatio = arabicMatches / totalChars;
    const englishRatio = englishMatches / totalChars;
    
    if (arabicRatio > 0.3 && englishRatio > 0.3) return 'mixed';
    if (arabicRatio > englishRatio) return 'arabic';
    if (englishRatio > arabicRatio) return 'english';
    
    return 'unknown';
  }

  /**
   * Get OCR statistics
   */
  getStatistics() {
    return {
      isInitialized: this.isInitialized,
      activeWorkers: this.workers.size,
      supportedLanguages: config.get('ocr.languages')
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      for (const [name, worker] of this.workers) {
        await worker.terminate();
        this.logger.info(`OCR worker '${name}' terminated`);
      }
      
      this.workers.clear();
      this.isInitialized = false;
      
    } catch (error) {
      this.logger.error(`OCR cleanup failed: ${error.message}`);
    }
  }
}

module.exports = OCRService;
