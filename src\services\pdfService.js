const fs = require('fs-extra');
const path = require('path');
const { PDFDocument } = require('pdf-lib');
const pdfParse = require('pdf-parse');
const { getDocument } = require('pdfjs-dist/legacy/build/pdf');
const winston = require('winston');
const config = require('config');

class PDFService {
  constructor() {
    this.logger = winston.createLogger({
      level: config.get('logging.level'),
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: config.get('logging.file.filename') })
      ]
    });
  }

  /**
   * Extract text and formatting from PDF
   * @param {string} pdfPath - Path to PDF file
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} Extracted content with formatting
   */
  async extractContent(pdfPath, options = {}) {
    try {
      this.logger.info(`Starting PDF content extraction for: ${pdfPath}`);
      
      const pdfBuffer = await fs.readFile(pdfPath);
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      
      // Get PDF metadata
      const metadata = await this.extractMetadata(pdfDoc);
      
      // Try text extraction first
      const textContent = await this.extractTextContent(pdfBuffer);
      
      // Analyze text extraction quality
      const extractionQuality = this.analyzeExtractionQuality(textContent);
      
      let content = {
        metadata,
        pages: [],
        extractionMethod: 'text',
        quality: extractionQuality,
        requiresOCR: extractionQuality.textRatio < config.get('pdf.textExtractionThreshold')
      };

      // Extract detailed content from each page
      for (let i = 0; i < pdfDoc.getPageCount(); i++) {
        const pageContent = await this.extractPageContent(pdfDoc, i, pdfBuffer);
        content.pages.push(pageContent);
      }

      this.logger.info(`PDF extraction completed. Quality: ${extractionQuality.textRatio}`);
      return content;

    } catch (error) {
      this.logger.error(`PDF extraction failed: ${error.message}`);
      throw new Error(`Failed to extract PDF content: ${error.message}`);
    }
  }

  /**
   * Extract metadata from PDF
   */
  async extractMetadata(pdfDoc) {
    try {
      const pageCount = pdfDoc.getPageCount();
      const firstPage = pdfDoc.getPage(0);
      const { width, height } = firstPage.getSize();

      return {
        pageCount,
        pageSize: { width, height },
        title: pdfDoc.getTitle() || '',
        author: pdfDoc.getAuthor() || '',
        subject: pdfDoc.getSubject() || '',
        creator: pdfDoc.getCreator() || '',
        producer: pdfDoc.getProducer() || '',
        creationDate: pdfDoc.getCreationDate(),
        modificationDate: pdfDoc.getModificationDate()
      };
    } catch (error) {
      this.logger.warn(`Failed to extract metadata: ${error.message}`);
      return {};
    }
  }

  /**
   * Extract text content using pdf-parse
   */
  async extractTextContent(pdfBuffer) {
    try {
      const data = await pdfParse(pdfBuffer);
      return {
        text: data.text,
        numPages: data.numpages,
        info: data.info,
        metadata: data.metadata
      };
    } catch (error) {
      this.logger.warn(`Text extraction failed: ${error.message}`);
      return { text: '', numPages: 0 };
    }
  }

  /**
   * Extract detailed content from a specific page
   */
  async extractPageContent(pdfDoc, pageIndex, pdfBuffer) {
    try {
      const page = pdfDoc.getPage(pageIndex);
      const { width, height } = page.getSize();

      // Extract text with positioning using PDF.js
      const textContent = await this.extractPageTextWithPositions(pdfBuffer, pageIndex);
      
      // Extract fonts and styles
      const fonts = await this.extractPageFonts(pdfBuffer, pageIndex);
      
      // Extract images
      const images = await this.extractPageImages(page);
      
      // Extract annotations
      const annotations = await this.extractPageAnnotations(page);

      return {
        pageNumber: pageIndex + 1,
        size: { width, height },
        textContent,
        fonts,
        images,
        annotations,
        hasText: textContent.items.length > 0,
        hasImages: images.length > 0
      };

    } catch (error) {
      this.logger.warn(`Page ${pageIndex + 1} extraction failed: ${error.message}`);
      return {
        pageNumber: pageIndex + 1,
        textContent: { items: [] },
        fonts: [],
        images: [],
        annotations: [],
        hasText: false,
        hasImages: false,
        error: error.message
      };
    }
  }

  /**
   * Extract text with positions using PDF.js
   */
  async extractPageTextWithPositions(pdfBuffer, pageIndex) {
    try {
      const loadingTask = getDocument({ data: pdfBuffer });
      const pdfDocument = await loadingTask.promise;
      const page = await pdfDocument.getPage(pageIndex + 1);
      
      const textContent = await page.getTextContent();
      
      // Process text items with formatting
      const processedItems = textContent.items.map(item => ({
        str: item.str,
        dir: item.dir,
        width: item.width,
        height: item.height,
        transform: item.transform,
        fontName: item.fontName,
        hasEOL: item.hasEOL,
        // Calculate position
        x: item.transform[4],
        y: item.transform[5],
        // Detect language
        language: this.detectLanguage(item.str),
        // Extract style information
        style: this.extractTextStyle(item)
      }));

      return {
        items: processedItems,
        styles: textContent.styles || {}
      };

    } catch (error) {
      this.logger.warn(`Text positioning extraction failed: ${error.message}`);
      return { items: [], styles: {} };
    }
  }

  /**
   * Extract font information from page
   */
  async extractPageFonts(pdfBuffer, pageIndex) {
    try {
      const loadingTask = getDocument({ data: pdfBuffer });
      const pdfDocument = await loadingTask.promise;
      const page = await pdfDocument.getPage(pageIndex + 1);
      
      const operatorList = await page.getOperatorList();
      const fonts = new Map();

      // Extract font information from operator list
      for (let i = 0; i < operatorList.fnArray.length; i++) {
        const fn = operatorList.fnArray[i];
        const args = operatorList.argsArray[i];
        
        if (fn === 12) { // setFont operation
          const fontName = args[0];
          const fontSize = args[1];
          fonts.set(fontName, { name: fontName, size: fontSize });
        }
      }

      return Array.from(fonts.values());

    } catch (error) {
      this.logger.warn(`Font extraction failed: ${error.message}`);
      return [];
    }
  }

  /**
   * Extract images from page
   */
  async extractPageImages(page) {
    try {
      // This is a simplified implementation
      // In a real scenario, you'd need to traverse the page's resource dictionary
      return [];
    } catch (error) {
      this.logger.warn(`Image extraction failed: ${error.message}`);
      return [];
    }
  }

  /**
   * Extract annotations from page
   */
  async extractPageAnnotations(page) {
    try {
      // This would extract annotations like comments, highlights, etc.
      return [];
    } catch (error) {
      this.logger.warn(`Annotation extraction failed: ${error.message}`);
      return [];
    }
  }

  /**
   * Analyze extraction quality
   */
  analyzeExtractionQuality(textContent) {
    const text = textContent.text || '';
    const totalChars = text.length;
    
    if (totalChars === 0) {
      return { textRatio: 0, confidence: 0, recommendation: 'ocr' };
    }

    // Count readable characters
    const readableChars = text.replace(/[^\w\s\u0600-\u06FF\u0750-\u077F]/g, '').length;
    const textRatio = readableChars / totalChars;
    
    // Detect Arabic content
    const arabicChars = (text.match(/[\u0600-\u06FF\u0750-\u077F]/g) || []).length;
    const hasArabic = arabicChars > 0;
    
    // Calculate confidence
    let confidence = textRatio;
    if (hasArabic && arabicChars / totalChars > 0.1) {
      confidence *= 1.2; // Boost confidence for Arabic content
    }

    return {
      textRatio,
      confidence: Math.min(confidence, 1),
      hasArabic,
      arabicRatio: arabicChars / totalChars,
      recommendation: textRatio >= config.get('pdf.textExtractionThreshold') ? 'text' : 'ocr'
    };
  }

  /**
   * Detect text language
   */
  detectLanguage(text) {
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F]/;
    const englishPattern = /[a-zA-Z]/;
    
    const hasArabic = arabicPattern.test(text);
    const hasEnglish = englishPattern.test(text);
    
    if (hasArabic && hasEnglish) return 'mixed';
    if (hasArabic) return 'arabic';
    if (hasEnglish) return 'english';
    return 'unknown';
  }

  /**
   * Extract style information from text item
   */
  extractTextStyle(item) {
    return {
      fontName: item.fontName || '',
      fontSize: Math.abs(item.transform[0]) || 12,
      bold: item.fontName ? item.fontName.toLowerCase().includes('bold') : false,
      italic: item.fontName ? item.fontName.toLowerCase().includes('italic') : false,
      color: item.color || '#000000'
    };
  }

  /**
   * Validate PDF file
   */
  async validatePDF(filePath) {
    try {
      const stats = await fs.stat(filePath);
      const maxSize = this.parseSize(config.get('pdf.maxFileSize'));
      
      if (stats.size > maxSize) {
        throw new Error(`File size exceeds maximum allowed size of ${config.get('pdf.maxFileSize')}`);
      }

      // Try to load the PDF to validate it
      const pdfBuffer = await fs.readFile(filePath);
      await PDFDocument.load(pdfBuffer);
      
      return true;
    } catch (error) {
      throw new Error(`Invalid PDF file: ${error.message}`);
    }
  }

  /**
   * Parse size string to bytes
   */
  parseSize(sizeStr) {
    const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
    if (!match) throw new Error(`Invalid size format: ${sizeStr}`);
    
    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();
    return value * units[unit];
  }
}

module.exports = PDFService;
