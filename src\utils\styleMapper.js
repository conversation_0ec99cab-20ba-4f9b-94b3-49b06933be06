/**
 * Style Mapper Utility
 * Maps PDF styles to DOCX styles with high fidelity
 */

class StyleMapper {
  constructor() {
    this.fontMappings = this.createFontMappings();
    this.colorMappings = this.createColorMappings();
    this.sizeMappings = this.createSizeMappings();
  }

  /**
   * Map PDF font to DOCX font
   */
  mapFont(pdfFont, language = 'english') {
    if (!pdfFont) {
      return language === 'arabic' ? 'Traditional Arabic' : 'Times New Roman';
    }

    // Clean font name
    const cleanFont = pdfFont.replace(/[+\-]/g, '').toLowerCase();
    
    // Check direct mappings
    if (this.fontMappings[cleanFont]) {
      return this.fontMappings[cleanFont];
    }

    // Check partial matches
    for (const [pattern, docxFont] of Object.entries(this.fontMappings)) {
      if (cleanFont.includes(pattern) || pattern.includes(cleanFont)) {
        return docxFont;
      }
    }

    // Language-specific fallbacks
    if (language === 'arabic') {
      if (cleanFont.includes('arab') || cleanFont.includes('nask')) {
        return 'Traditional Arabic';
      }
      return 'Arial Unicode MS';
    }

    // Default fallback
    return 'Times New Roman';
  }

  /**
   * Map PDF color to DOCX color
   */
  mapColor(pdfColor) {
    if (!pdfColor) return '000000';

    // Handle different color formats
    if (typeof pdfColor === 'string') {
      // Remove # if present
      let color = pdfColor.replace('#', '');
      
      // Convert 3-digit hex to 6-digit
      if (color.length === 3) {
        color = color.split('').map(c => c + c).join('');
      }
      
      // Validate hex color
      if (/^[0-9A-Fa-f]{6}$/.test(color)) {
        return color.toUpperCase();
      }
    }

    // Handle RGB array [r, g, b]
    if (Array.isArray(pdfColor) && pdfColor.length >= 3) {
      const [r, g, b] = pdfColor.map(c => Math.round(c * 255));
      return this.rgbToHex(r, g, b);
    }

    // Handle RGB object {r, g, b}
    if (typeof pdfColor === 'object' && pdfColor.r !== undefined) {
      const r = Math.round(pdfColor.r * 255);
      const g = Math.round(pdfColor.g * 255);
      const b = Math.round(pdfColor.b * 255);
      return this.rgbToHex(r, g, b);
    }

    // Check color name mappings
    const colorName = pdfColor.toString().toLowerCase();
    if (this.colorMappings[colorName]) {
      return this.colorMappings[colorName];
    }

    return '000000'; // Default black
  }

  /**
   * Map PDF font size to DOCX font size
   */
  mapFontSize(pdfSize) {
    if (!pdfSize || pdfSize <= 0) return 24; // 12pt in half-points

    // PDF size is in points, DOCX size is in half-points
    let docxSize = Math.round(pdfSize * 2);

    // Apply size constraints
    docxSize = Math.max(docxSize, 12); // Minimum 6pt
    docxSize = Math.min(docxSize, 144); // Maximum 72pt

    return docxSize;
  }

  /**
   * Map PDF text alignment to DOCX alignment
   */
  mapAlignment(pdfAlignment) {
    const alignmentMap = {
      'left': 'left',
      'center': 'center',
      'centre': 'center',
      'right': 'right',
      'justify': 'justify',
      'justified': 'justify',
      'start': 'left',
      'end': 'right'
    };

    if (!pdfAlignment) return 'left';
    
    const alignment = pdfAlignment.toString().toLowerCase();
    return alignmentMap[alignment] || 'left';
  }

  /**
   * Map PDF line spacing to DOCX line spacing
   */
  mapLineSpacing(pdfSpacing) {
    if (!pdfSpacing || pdfSpacing <= 0) return 240; // Single spacing

    // Convert to twentieths of a point
    let docxSpacing = Math.round(pdfSpacing * 240);
    
    // Common spacing values
    if (docxSpacing >= 220 && docxSpacing <= 260) return 240; // Single
    if (docxSpacing >= 340 && docxSpacing <= 380) return 360; // 1.5
    if (docxSpacing >= 460 && docxSpacing <= 500) return 480; // Double

    return Math.max(docxSpacing, 120); // Minimum 0.5 spacing
  }

  /**
   * Map PDF indentation to DOCX indentation
   */
  mapIndentation(pdfIndent) {
    if (!pdfIndent) return { left: 0, right: 0, firstLine: 0 };

    const result = {
      left: this.pointsToTwips(pdfIndent.left || 0),
      right: this.pointsToTwips(pdfIndent.right || 0),
      firstLine: this.pointsToTwips(pdfIndent.firstLine || 0)
    };

    // Ensure non-negative values
    result.left = Math.max(result.left, 0);
    result.right = Math.max(result.right, 0);

    return result;
  }

  /**
   * Detect and map text style properties
   */
  mapTextStyle(pdfStyle) {
    const style = {
      bold: false,
      italic: false,
      underline: false,
      strikethrough: false
    };

    if (!pdfStyle) return style;

    const fontName = (pdfStyle.fontName || '').toLowerCase();
    const fontWeight = (pdfStyle.fontWeight || '').toLowerCase();
    const fontStyle = (pdfStyle.fontStyle || '').toLowerCase();
    const textDecoration = (pdfStyle.textDecoration || '').toLowerCase();

    // Detect bold
    style.bold = fontName.includes('bold') || 
                 fontWeight.includes('bold') || 
                 fontWeight.includes('700') ||
                 fontWeight.includes('800') ||
                 fontWeight.includes('900');

    // Detect italic
    style.italic = fontName.includes('italic') || 
                   fontName.includes('oblique') ||
                   fontStyle.includes('italic') ||
                   fontStyle.includes('oblique');

    // Detect underline
    style.underline = textDecoration.includes('underline') ||
                      pdfStyle.underline === true;

    // Detect strikethrough
    style.strikethrough = textDecoration.includes('line-through') ||
                          textDecoration.includes('strikethrough') ||
                          pdfStyle.strikethrough === true;

    return style;
  }

  /**
   * Create comprehensive font mappings
   */
  createFontMappings() {
    return {
      // Arabic fonts
      'traditionalarabic': 'Traditional Arabic',
      'arabictypesetting': 'Arabic Typesetting',
      'microsoftsansserif': 'Microsoft Sans Serif',
      'tahoma': 'Tahoma',
      'arial': 'Arial',
      'arialunicodems': 'Arial Unicode MS',
      'naskh': 'Traditional Arabic',
      'kufi': 'Traditional Arabic',
      'thuluth': 'Traditional Arabic',

      // English fonts
      'times': 'Times New Roman',
      'timesnewroman': 'Times New Roman',
      'helvetica': 'Arial',
      'arial': 'Arial',
      'courier': 'Courier New',
      'couriernew': 'Courier New',
      'calibri': 'Calibri',
      'cambria': 'Cambria',
      'georgia': 'Georgia',
      'verdana': 'Verdana',
      'trebuchet': 'Trebuchet MS',
      'comicsans': 'Comic Sans MS',
      'impact': 'Impact',
      'lucida': 'Lucida Console',

      // Generic mappings
      'serif': 'Times New Roman',
      'sansserif': 'Arial',
      'monospace': 'Courier New',
      'cursive': 'Comic Sans MS',
      'fantasy': 'Impact'
    };
  }

  /**
   * Create color name mappings
   */
  createColorMappings() {
    return {
      'black': '000000',
      'white': 'FFFFFF',
      'red': 'FF0000',
      'green': '008000',
      'blue': '0000FF',
      'yellow': 'FFFF00',
      'cyan': '00FFFF',
      'magenta': 'FF00FF',
      'gray': '808080',
      'grey': '808080',
      'darkgray': '404040',
      'darkgrey': '404040',
      'lightgray': 'C0C0C0',
      'lightgrey': 'C0C0C0',
      'orange': 'FFA500',
      'purple': '800080',
      'brown': 'A52A2A',
      'pink': 'FFC0CB',
      'lime': '00FF00',
      'navy': '000080',
      'maroon': '800000',
      'olive': '808000',
      'teal': '008080',
      'silver': 'C0C0C0',
      'gold': 'FFD700'
    };
  }

  /**
   * Create size mappings for common adjustments
   */
  createSizeMappings() {
    return {
      'xx-small': 16, // 8pt
      'x-small': 20,  // 10pt
      'small': 22,    // 11pt
      'medium': 24,   // 12pt
      'large': 36,    // 18pt
      'x-large': 48,  // 24pt
      'xx-large': 64  // 32pt
    };
  }

  /**
   * Convert RGB values to hex
   */
  rgbToHex(r, g, b) {
    const toHex = (n) => {
      const hex = Math.max(0, Math.min(255, n)).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    
    return (toHex(r) + toHex(g) + toHex(b)).toUpperCase();
  }

  /**
   * Convert points to twentieths of a point (twips)
   */
  pointsToTwips(points) {
    return Math.round(points * 20);
  }

  /**
   * Convert pixels to points (assuming 96 DPI)
   */
  pixelsToPoints(pixels, dpi = 96) {
    return (pixels * 72) / dpi;
  }

  /**
   * Normalize font weight
   */
  normalizeFontWeight(weight) {
    if (typeof weight === 'number') {
      return weight >= 600;
    }
    
    const weightStr = weight.toString().toLowerCase();
    return weightStr.includes('bold') || 
           weightStr.includes('700') || 
           weightStr.includes('800') || 
           weightStr.includes('900');
  }

  /**
   * Get style confidence score
   */
  getStyleConfidence(pdfStyle) {
    let confidence = 0.5; // Base confidence
    
    if (pdfStyle.fontName) confidence += 0.2;
    if (pdfStyle.fontSize) confidence += 0.1;
    if (pdfStyle.color) confidence += 0.1;
    if (pdfStyle.bold !== undefined) confidence += 0.05;
    if (pdfStyle.italic !== undefined) confidence += 0.05;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Create style fingerprint for caching
   */
  createStyleFingerprint(pdfStyle) {
    const parts = [
      pdfStyle.fontName || 'default',
      pdfStyle.fontSize || '12',
      pdfStyle.color || '000000',
      pdfStyle.bold ? 'bold' : 'normal',
      pdfStyle.italic ? 'italic' : 'normal'
    ];
    
    return parts.join('|');
  }

  /**
   * Validate mapped style
   */
  validateMappedStyle(mappedStyle) {
    const errors = [];
    
    if (!mappedStyle.font) {
      errors.push('Missing font family');
    }
    
    if (!mappedStyle.size || mappedStyle.size < 12 || mappedStyle.size > 144) {
      errors.push('Invalid font size');
    }
    
    if (!mappedStyle.color || !/^[0-9A-Fa-f]{6}$/.test(mappedStyle.color)) {
      errors.push('Invalid color format');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

module.exports = StyleMapper;
