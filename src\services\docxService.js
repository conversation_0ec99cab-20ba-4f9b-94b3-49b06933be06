const fs = require('fs-extra');
const path = require('path');
const { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, ImageRun, AlignmentType, HeadingLevel, BorderStyle, WidthType } = require('docx');
const winston = require('winston');
const config = require('config');

class DOCXService {
  constructor() {
    this.logger = winston.createLogger({
      level: config.get('logging.level'),
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: config.get('logging.file.filename') })
      ]
    });
  }

  /**
   * Create DOCX document from analyzed content
   */
  async createDocument(analysisResult, options = {}) {
    try {
      this.logger.info('Starting DOCX document creation');
      
      const sections = await this.createSections(analysisResult, options);
      
      const doc = new Document({
        creator: 'Arabic PDF2Word Pro v2',
        title: options.title || 'Converted Document',
        description: 'Document converted from PDF with formatting preservation',
        sections: sections,
        styles: this.createDocumentStyles(),
        numbering: this.createNumberingDefinitions()
      });

      const buffer = await Packer.toBuffer(doc);
      this.logger.info('DOCX document creation completed');
      return buffer;

    } catch (error) {
      this.logger.error(`DOCX creation failed: ${error.message}`);
      throw new Error(`Failed to create DOCX document: ${error.message}`);
    }
  }

  /**
   * Create document sections from analysis
   */
  async createSections(analysisResult, options = {}) {
    const sections = [];
    
    for (const pageAnalysis of analysisResult.pages) {
      const section = await this.createPageSection(pageAnalysis, options);
      sections.push(section);
    }

    return sections;
  }

  /**
   * Create a section for a single page
   */
  async createPageSection(pageAnalysis, options = {}) {
    const children = [];
    
    // Process elements in reading order
    if (pageAnalysis.layout && pageAnalysis.layout.readingOrder) {
      for (const orderItem of pageAnalysis.layout.readingOrder) {
        const element = this.findElementById(pageAnalysis.layout.elements, orderItem.elementId);
        if (element) {
          const docElement = await this.createElement(element, orderItem.elementType, options);
          if (docElement) {
            if (Array.isArray(docElement)) {
              children.push(...docElement);
            } else {
              children.push(docElement);
            }
          }
        }
      }
    } else {
      // Fallback: process elements by type
      children.push(...await this.createFallbackContent(pageAnalysis, options));
    }

    return {
      properties: {
        page: {
          margin: config.get('docx.pageMargins')
        }
      },
      children: children
    };
  }

  /**
   * Create document element based on type
   */
  async createElement(element, elementType, options = {}) {
    switch (elementType) {
      case 'paragraph':
        return this.createParagraphElement(element, options);
      case 'header':
        return this.createHeaderElement(element, options);
      case 'table':
        return this.createTableElement(element, options);
      case 'image':
        return await this.createImageElement(element, options);
      case 'list':
        return this.createListElement(element, options);
      default:
        return this.createParagraphElement(element, options);
    }
  }

  /**
   * Create paragraph element
   */
  createParagraphElement(paragraph, options = {}) {
    if (!paragraph.runs || paragraph.runs.length === 0) {
      return new Paragraph({
        children: [new TextRun({ text: paragraph.text || '' })],
        alignment: this.mapAlignment(paragraph.alignment),
        bidirectional: paragraph.language === 'arabic' || paragraph.language === 'mixed'
      });
    }

    const textRuns = paragraph.runs.map(run => this.createTextRun(run));
    
    return new Paragraph({
      children: textRuns,
      alignment: this.mapAlignment(paragraph.alignment),
      bidirectional: paragraph.language === 'arabic' || paragraph.language === 'mixed'
    });
  }

  /**
   * Create header element
   */
  createHeaderElement(header, options = {}) {
    return new Paragraph({
      children: [this.createTextRun({
        text: header.text,
        style: header.style
      })],
      heading: this.mapHeadingLevel(header.level),
      alignment: this.mapAlignment('center'),
      bidirectional: header.language === 'arabic'
    });
  }

  /**
   * Create table element
   */
  createTableElement(table, options = {}) {
    if (!table.rows || table.rows.length === 0) {
      return null;
    }

    const tableRows = table.rows.map(row => this.createTableRow(row));
    
    return new Table({
      rows: tableRows,
      width: {
        size: 100,
        type: WidthType.PERCENTAGE
      },
      borders: {
        top: { style: BorderStyle.SINGLE, size: 1 },
        bottom: { style: BorderStyle.SINGLE, size: 1 },
        left: { style: BorderStyle.SINGLE, size: 1 },
        right: { style: BorderStyle.SINGLE, size: 1 }
      }
    });
  }

  /**
   * Create table row
   */
  createTableRow(row) {
    const cells = row.cells.map(cell => this.createTableCell(cell));
    return new TableRow({ children: cells });
  }

  /**
   * Create table cell
   */
  createTableCell(cell) {
    const paragraphs = [];
    
    if (cell.text) {
      paragraphs.push(new Paragraph({
        children: [new TextRun({ text: cell.text })],
        bidirectional: this.detectLanguage(cell.text) === 'arabic'
      }));
    }

    return new TableCell({
      children: paragraphs.length > 0 ? paragraphs : [new Paragraph({ children: [new TextRun({ text: '' })] })]
    });
  }

  /**
   * Create image element
   */
  async createImageElement(image, options = {}) {
    try {
      if (!image.buffer && !image.path) {
        return null;
      }

      let imageBuffer = image.buffer;
      if (!imageBuffer && image.path) {
        imageBuffer = await fs.readFile(image.path);
      }

      const imageRun = new ImageRun({
        data: imageBuffer,
        transformation: {
          width: image.width || 400,
          height: image.height || 300
        }
      });

      return new Paragraph({
        children: [imageRun],
        alignment: AlignmentType.CENTER
      });

    } catch (error) {
      this.logger.warn(`Failed to create image element: ${error.message}`);
      return new Paragraph({
        children: [new TextRun({ text: `[Image: ${image.description || 'Unable to load'}]` })]
      });
    }
  }

  /**
   * Create list element
   */
  createListElement(list, options = {}) {
    const paragraphs = [];
    
    list.items.forEach((item, index) => {
      const paragraph = new Paragraph({
        children: [new TextRun({ text: item.text })],
        numbering: {
          reference: list.type === 'ordered' ? 'ordered-list' : 'bullet-list',
          level: 0
        },
        bidirectional: item.language === 'arabic'
      });
      
      paragraphs.push(paragraph);
    });

    return paragraphs;
  }

  /**
   * Create text run with formatting
   */
  createTextRun(run) {
    const style = run.style || {};
    
    return new TextRun({
      text: run.text,
      bold: style.bold || false,
      italics: style.italic || false,
      underline: style.underline ? {} : undefined,
      size: this.mapFontSize(style.fontSize),
      font: this.mapFontName(style.fontName, run.language),
      color: this.mapColor(style.color),
      rightToLeft: run.language === 'arabic'
    });
  }

  /**
   * Create fallback content when reading order is not available
   */
  async createFallbackContent(pageAnalysis, options = {}) {
    const children = [];
    const elements = pageAnalysis.layout?.elements || {};

    // Add headers first
    if (elements.headers) {
      for (const header of elements.headers) {
        const headerElement = this.createHeaderElement(header, options);
        children.push(headerElement);
      }
    }

    // Add paragraphs
    if (elements.paragraphs) {
      for (const paragraph of elements.paragraphs) {
        const paragraphElement = this.createParagraphElement(paragraph, options);
        children.push(paragraphElement);
      }
    }

    // Add tables
    if (elements.tables) {
      for (const table of elements.tables) {
        const tableElement = this.createTableElement(table, options);
        if (tableElement) children.push(tableElement);
      }
    }

    return children;
  }

  /**
   * Create document styles
   */
  createDocumentStyles() {
    return {
      paragraphStyles: [
        {
          id: 'Normal',
          name: 'Normal',
          basedOn: 'Normal',
          next: 'Normal',
          run: {
            font: config.get('docx.defaultFont.english'),
            size: config.get('docx.defaultFontSize') * 2
          }
        }
      ]
    };
  }

  /**
   * Create numbering definitions for lists
   */
  createNumberingDefinitions() {
    return {
      config: [
        {
          reference: 'bullet-list',
          levels: [
            {
              level: 0,
              format: 'bullet',
              text: '•',
              alignment: AlignmentType.LEFT
            }
          ]
        }
      ]
    };
  }

  // Mapping functions
  mapAlignment(alignment) {
    switch (alignment) {
      case 'center': return AlignmentType.CENTER;
      case 'right': return AlignmentType.RIGHT;
      case 'justify': return AlignmentType.JUSTIFIED;
      default: return AlignmentType.LEFT;
    }
  }

  mapHeadingLevel(level) {
    switch (level) {
      case 1: return HeadingLevel.HEADING_1;
      case 2: return HeadingLevel.HEADING_2;
      case 3: return HeadingLevel.HEADING_3;
      default: return HeadingLevel.HEADING_1;
    }
  }

  mapFontSize(fontSize) {
    return Math.round((fontSize || config.get('docx.defaultFontSize')) * 2);
  }

  mapFontName(fontName, language) {
    if (!fontName) {
      return language === 'arabic' ? 
        config.get('docx.defaultFont.arabic') : 
        config.get('docx.defaultFont.english');
    }
    return fontName;
  }

  mapColor(color) {
    if (!color) return '000000';
    return color.replace('#', '').toUpperCase();
  }

  detectLanguage(text) {
    if (!text) return 'english';
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F]/;
    return arabicPattern.test(text) ? 'arabic' : 'english';
  }

  findElementById(elements, elementId) {
    for (const [type, elementArray] of Object.entries(elements)) {
      const element = elementArray.find(el => el.id === elementId);
      if (element) return element;
    }
    return null;
  }

  /**
   * Save document to file
   */
  async saveDocument(docBuffer, outputPath) {
    try {
      await fs.ensureDir(path.dirname(outputPath));
      await fs.writeFile(outputPath, docBuffer);
      this.logger.info(`Document saved to: ${outputPath}`);
    } catch (error) {
      this.logger.error(`Failed to save document: ${error.message}`);
      throw error;
    }
  }
}

module.exports = DOCXService;
