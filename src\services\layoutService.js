const winston = require('winston');
const config = require('config');
const _ = require('lodash');

class LayoutService {
  constructor() {
    this.logger = winston.createLogger({
      level: config.get('logging.level'),
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: config.get('logging.file.filename') })
      ]
    });
  }

  /**
   * Analyze page layout and extract structured elements
   * @param {Object} pageContent - Page content from PDF service
   * @returns {Promise<Object>} Structured layout analysis
   */
  async analyzePageLayout(pageContent) {
    try {
      this.logger.info(`Analyzing layout for page ${pageContent.pageNumber}`);

      const analysis = {
        pageNumber: pageContent.pageNumber,
        pageSize: pageContent.size,
        elements: {
          paragraphs: [],
          tables: [],
          images: [],
          headers: [],
          footers: [],
          lists: [],
          columns: []
        },
        readingOrder: [],
        textDirection: 'auto'
      };

      // Analyze text elements
      if (pageContent.textContent && pageContent.textContent.items.length > 0) {
        analysis.elements.paragraphs = this.extractParagraphs(pageContent.textContent);
        analysis.elements.headers = this.extractHeaders(pageContent.textContent);
        analysis.elements.footers = this.extractFooters(pageContent.textContent, pageContent.size);
        analysis.elements.lists = this.extractLists(pageContent.textContent);
        analysis.elements.tables = this.extractTables(pageContent.textContent);
        analysis.elements.columns = this.detectColumns(pageContent.textContent);
        analysis.textDirection = this.detectTextDirection(pageContent.textContent);
      }

      // Analyze images
      if (pageContent.images && pageContent.images.length > 0) {
        analysis.elements.images = this.analyzeImages(pageContent.images);
      }

      // Determine reading order
      analysis.readingOrder = this.determineReadingOrder(analysis.elements, analysis.textDirection);

      this.logger.info(`Layout analysis completed for page ${pageContent.pageNumber}`);
      return analysis;

    } catch (error) {
      this.logger.error(`Layout analysis failed for page ${pageContent.pageNumber}: ${error.message}`);
      throw new Error(`Layout analysis failed: ${error.message}`);
    }
  }

  /**
   * Extract paragraphs from text content
   */
  extractParagraphs(textContent) {
    const paragraphs = [];
    const items = textContent.items;
    
    if (!items || items.length === 0) return paragraphs;

    let currentParagraph = {
      id: `para_${paragraphs.length + 1}`,
      text: '',
      runs: [],
      bounds: { left: Infinity, top: Infinity, right: -Infinity, bottom: -Infinity },
      language: 'unknown',
      alignment: 'left',
      lineSpacing: 1.0,
      indentation: { left: 0, right: 0, firstLine: 0 }
    };

    let lastY = null;
    const lineThreshold = 5; // pixels

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const nextItem = items[i + 1];

      // Check if this starts a new paragraph
      if (lastY !== null && Math.abs(item.y - lastY) > lineThreshold * 2) {
        if (currentParagraph.text.trim()) {
          this.finalizeParagraph(currentParagraph);
          paragraphs.push(currentParagraph);
        }
        currentParagraph = this.createNewParagraph(paragraphs.length + 1);
      }

      // Add text run to current paragraph
      const run = {
        text: item.str,
        style: item.style || {},
        position: { x: item.x, y: item.y },
        language: item.language || 'unknown'
      };

      currentParagraph.runs.push(run);
      currentParagraph.text += item.str;
      
      // Update paragraph bounds
      this.updateBounds(currentParagraph.bounds, item);
      
      // Update paragraph language
      if (item.language && item.language !== 'unknown') {
        currentParagraph.language = item.language;
      }

      lastY = item.y;

      // Check for paragraph break
      if (item.hasEOL || (nextItem && Math.abs(nextItem.y - item.y) > lineThreshold * 1.5)) {
        if (currentParagraph.text.trim()) {
          this.finalizeParagraph(currentParagraph);
          paragraphs.push(currentParagraph);
          currentParagraph = this.createNewParagraph(paragraphs.length + 1);
        }
      }
    }

    // Add final paragraph if it has content
    if (currentParagraph.text.trim()) {
      this.finalizeParagraph(currentParagraph);
      paragraphs.push(currentParagraph);
    }

    return paragraphs;
  }

  /**
   * Create a new paragraph object
   */
  createNewParagraph(id) {
    return {
      id: `para_${id}`,
      text: '',
      runs: [],
      bounds: { left: Infinity, top: Infinity, right: -Infinity, bottom: -Infinity },
      language: 'unknown',
      alignment: 'left',
      lineSpacing: 1.0,
      indentation: { left: 0, right: 0, firstLine: 0 }
    };
  }

  /**
   * Finalize paragraph properties
   */
  finalizeParagraph(paragraph) {
    // Determine alignment based on text position
    paragraph.alignment = this.determineAlignment(paragraph);
    
    // Calculate line spacing
    paragraph.lineSpacing = this.calculateLineSpacing(paragraph);
    
    // Calculate indentation
    paragraph.indentation = this.calculateIndentation(paragraph);
    
    // Clean up text
    paragraph.text = paragraph.text.trim();
  }

  /**
   * Extract headers from text content
   */
  extractHeaders(textContent) {
    const headers = [];
    const items = textContent.items;
    
    // Look for text items that appear to be headers (larger font, bold, at top of page)
    const sortedItems = items.sort((a, b) => b.y - a.y); // Sort by Y position (top to bottom)
    
    for (let i = 0; i < Math.min(5, sortedItems.length); i++) {
      const item = sortedItems[i];
      const style = item.style || {};
      
      // Check if this looks like a header
      if (this.isLikelyHeader(item, style)) {
        headers.push({
          id: `header_${headers.length + 1}`,
          text: item.str,
          level: this.determineHeaderLevel(style),
          style: style,
          position: { x: item.x, y: item.y },
          language: item.language || 'unknown'
        });
      }
    }

    return headers;
  }

  /**
   * Extract footers from text content
   */
  extractFooters(textContent, pageSize) {
    const footers = [];
    const items = textContent.items;
    const footerThreshold = pageSize.height * 0.1; // Bottom 10% of page
    
    const footerItems = items.filter(item => item.y < footerThreshold);
    
    footerItems.forEach((item, index) => {
      footers.push({
        id: `footer_${index + 1}`,
        text: item.str,
        style: item.style || {},
        position: { x: item.x, y: item.y },
        language: item.language || 'unknown'
      });
    });

    return footers;
  }

  /**
   * Extract lists from text content
   */
  extractLists(textContent) {
    const lists = [];
    const items = textContent.items;
    
    // Look for bullet points or numbered lists
    const listPattern = /^[\u2022\u2023\u25E6\u2043\u2219•·‣⁃]\s*|^\d+[\.\)]\s*|^[a-zA-Z][\.\)]\s*/;
    
    let currentList = null;
    
    items.forEach(item => {
      if (listPattern.test(item.str)) {
        if (!currentList) {
          currentList = {
            id: `list_${lists.length + 1}`,
            type: this.determineListType(item.str),
            items: [],
            style: item.style || {}
          };
        }
        
        currentList.items.push({
          text: item.str.replace(listPattern, '').trim(),
          marker: item.str.match(listPattern)[0].trim(),
          position: { x: item.x, y: item.y },
          language: item.language || 'unknown'
        });
      } else if (currentList) {
        lists.push(currentList);
        currentList = null;
      }
    });
    
    if (currentList) {
      lists.push(currentList);
    }

    return lists;
  }

  /**
   * Extract tables from text content
   */
  extractTables(textContent) {
    const tables = [];
    const items = textContent.items;
    
    // Group items by Y position to find potential table rows
    const rowGroups = this.groupItemsByY(items);
    
    // Look for patterns that suggest table structure
    const potentialTables = this.identifyTableStructures(rowGroups);
    
    potentialTables.forEach((tableData, index) => {
      const table = {
        id: `table_${index + 1}`,
        rows: tableData.rows,
        columns: tableData.columns,
        bounds: tableData.bounds,
        style: {
          borders: true,
          cellPadding: 5,
          alignment: 'left'
        }
      };
      
      tables.push(table);
    });

    return tables;
  }

  /**
   * Detect column layout
   */
  detectColumns(textContent) {
    const items = textContent.items;
    if (!items || items.length === 0) return [];

    // Group items by X position to detect columns
    const xPositions = items.map(item => item.x).sort((a, b) => a - b);
    const columnBreaks = this.findColumnBreaks(xPositions);
    
    const columns = [];
    for (let i = 0; i < columnBreaks.length - 1; i++) {
      const columnItems = items.filter(item => 
        item.x >= columnBreaks[i] && item.x < columnBreaks[i + 1]
      );
      
      if (columnItems.length > 0) {
        columns.push({
          id: `column_${i + 1}`,
          bounds: {
            left: columnBreaks[i],
            right: columnBreaks[i + 1],
            top: Math.max(...columnItems.map(item => item.y)),
            bottom: Math.min(...columnItems.map(item => item.y))
          },
          items: columnItems
        });
      }
    }

    return columns;
  }

  /**
   * Detect overall text direction
   */
  detectTextDirection(textContent) {
    const items = textContent.items;
    let arabicCount = 0;
    let englishCount = 0;
    
    items.forEach(item => {
      if (item.language === 'arabic') arabicCount++;
      else if (item.language === 'english') englishCount++;
    });
    
    if (arabicCount > englishCount) return 'rtl';
    if (englishCount > arabicCount) return 'ltr';
    return 'auto';
  }

  /**
   * Determine reading order of elements
   */
  determineReadingOrder(elements, textDirection) {
    const allElements = [
      ...elements.headers,
      ...elements.paragraphs,
      ...elements.tables,
      ...elements.images,
      ...elements.lists
    ];

    // Sort by Y position (top to bottom), then by X position based on text direction
    return allElements.sort((a, b) => {
      const yDiff = (b.position?.y || b.bounds?.top || 0) - (a.position?.y || a.bounds?.top || 0);
      if (Math.abs(yDiff) > 10) return yDiff; // Different lines
      
      // Same line - sort by X based on text direction
      const aX = a.position?.x || a.bounds?.left || 0;
      const bX = b.position?.x || b.bounds?.left || 0;
      
      return textDirection === 'rtl' ? bX - aX : aX - bX;
    }).map((element, index) => ({
      order: index + 1,
      elementId: element.id,
      elementType: this.getElementType(element)
    }));
  }

  // Helper methods
  updateBounds(bounds, item) {
    bounds.left = Math.min(bounds.left, item.x);
    bounds.right = Math.max(bounds.right, item.x + (item.width || 0));
    bounds.top = Math.max(bounds.top, item.y);
    bounds.bottom = Math.min(bounds.bottom, item.y - (item.height || 0));
  }

  isLikelyHeader(item, style) {
    const fontSize = style.fontSize || 12;
    const isBold = style.bold || false;
    const isLarge = fontSize > 14;
    
    return isBold || isLarge || item.str.length < 100;
  }

  determineHeaderLevel(style) {
    const fontSize = style.fontSize || 12;
    if (fontSize >= 20) return 1;
    if (fontSize >= 16) return 2;
    if (fontSize >= 14) return 3;
    return 4;
  }

  determineListType(text) {
    if (/^\d+/.test(text)) return 'ordered';
    if (/^[a-zA-Z]/.test(text)) return 'alpha';
    return 'unordered';
  }

  determineAlignment(paragraph) {
    if (!paragraph.runs || paragraph.runs.length === 0) return 'left';
    
    const leftMost = Math.min(...paragraph.runs.map(run => run.position.x));
    const rightMost = Math.max(...paragraph.runs.map(run => run.position.x));
    const center = (leftMost + rightMost) / 2;
    
    // Simple heuristic for alignment detection
    if (Math.abs(center - 300) < 50) return 'center'; // Assuming 600px page width
    if (leftMost < 100) return 'left';
    return 'right';
  }

  calculateLineSpacing(paragraph) {
    if (!paragraph.runs || paragraph.runs.length < 2) return 1.0;
    
    const yPositions = paragraph.runs.map(run => run.position.y).sort((a, b) => b - a);
    const spacings = [];
    
    for (let i = 1; i < yPositions.length; i++) {
      spacings.push(yPositions[i - 1] - yPositions[i]);
    }
    
    const avgSpacing = spacings.reduce((sum, spacing) => sum + spacing, 0) / spacings.length;
    return Math.max(1.0, avgSpacing / 12); // Normalize to font size
  }

  calculateIndentation(paragraph) {
    if (!paragraph.runs || paragraph.runs.length === 0) {
      return { left: 0, right: 0, firstLine: 0 };
    }
    
    const leftMost = Math.min(...paragraph.runs.map(run => run.position.x));
    const firstLineX = paragraph.runs[0].position.x;
    
    return {
      left: Math.max(0, leftMost - 50), // Assuming 50px as base margin
      right: 0,
      firstLine: firstLineX - leftMost
    };
  }

  groupItemsByY(items, threshold = 5) {
    const groups = [];
    const sortedItems = items.sort((a, b) => b.y - a.y);
    
    let currentGroup = [];
    let lastY = null;
    
    sortedItems.forEach(item => {
      if (lastY === null || Math.abs(item.y - lastY) <= threshold) {
        currentGroup.push(item);
      } else {
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [item];
      }
      lastY = item.y;
    });
    
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    
    return groups;
  }

  identifyTableStructures(rowGroups) {
    // Simplified table detection - look for rows with similar column patterns
    const tables = [];
    
    for (let i = 0; i < rowGroups.length - 1; i++) {
      const currentRow = rowGroups[i];
      const nextRow = rowGroups[i + 1];
      
      if (this.haveSimilarColumnStructure(currentRow, nextRow)) {
        // Found potential table
        const tableRows = [currentRow, nextRow];
        
        // Look for more rows
        for (let j = i + 2; j < rowGroups.length; j++) {
          if (this.haveSimilarColumnStructure(currentRow, rowGroups[j])) {
            tableRows.push(rowGroups[j]);
          } else {
            break;
          }
        }
        
        if (tableRows.length >= 2) {
          tables.push(this.createTableStructure(tableRows));
        }
      }
    }
    
    return tables;
  }

  haveSimilarColumnStructure(row1, row2, tolerance = 20) {
    if (Math.abs(row1.length - row2.length) > 1) return false;
    
    const x1Positions = row1.map(item => item.x).sort((a, b) => a - b);
    const x2Positions = row2.map(item => item.x).sort((a, b) => a - b);
    
    for (let i = 0; i < Math.min(x1Positions.length, x2Positions.length); i++) {
      if (Math.abs(x1Positions[i] - x2Positions[i]) > tolerance) {
        return false;
      }
    }
    
    return true;
  }

  createTableStructure(tableRows) {
    const columns = this.identifyTableColumns(tableRows);
    const rows = tableRows.map((rowItems, rowIndex) => ({
      id: `row_${rowIndex + 1}`,
      cells: this.createTableCells(rowItems, columns)
    }));
    
    return {
      rows,
      columns: columns.length,
      bounds: this.calculateTableBounds(tableRows)
    };
  }

  identifyTableColumns(tableRows) {
    const allXPositions = [];
    tableRows.forEach(row => {
      row.forEach(item => allXPositions.push(item.x));
    });
    
    return this.findColumnBreaks(allXPositions.sort((a, b) => a - b));
  }

  findColumnBreaks(xPositions, minGap = 30) {
    const breaks = [Math.min(...xPositions)];
    
    for (let i = 1; i < xPositions.length; i++) {
      if (xPositions[i] - xPositions[i - 1] > minGap) {
        breaks.push(xPositions[i]);
      }
    }
    
    breaks.push(Math.max(...xPositions) + 100); // Add end boundary
    return breaks;
  }

  createTableCells(rowItems, columnBreaks) {
    const cells = [];
    
    for (let i = 0; i < columnBreaks.length - 1; i++) {
      const cellItems = rowItems.filter(item => 
        item.x >= columnBreaks[i] && item.x < columnBreaks[i + 1]
      );
      
      cells.push({
        id: `cell_${i + 1}`,
        text: cellItems.map(item => item.str).join(' '),
        items: cellItems,
        bounds: {
          left: columnBreaks[i],
          right: columnBreaks[i + 1]
        }
      });
    }
    
    return cells;
  }

  calculateTableBounds(tableRows) {
    const allItems = tableRows.flat();
    return {
      left: Math.min(...allItems.map(item => item.x)),
      right: Math.max(...allItems.map(item => item.x + (item.width || 0))),
      top: Math.max(...allItems.map(item => item.y)),
      bottom: Math.min(...allItems.map(item => item.y))
    };
  }

  analyzeImages(images) {
    return images.map((image, index) => ({
      id: `image_${index + 1}`,
      ...image,
      type: 'image',
      description: `Image ${index + 1}`
    }));
  }

  getElementType(element) {
    if (element.id.startsWith('para_')) return 'paragraph';
    if (element.id.startsWith('header_')) return 'header';
    if (element.id.startsWith('table_')) return 'table';
    if (element.id.startsWith('image_')) return 'image';
    if (element.id.startsWith('list_')) return 'list';
    return 'unknown';
  }
}

module.exports = LayoutService;
