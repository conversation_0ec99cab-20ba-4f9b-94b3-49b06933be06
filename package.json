{"name": "arabic-pdf2word-pro-v2", "version": "2.0.0", "description": "Enterprise-grade PDF to DOCX converter with 100% Arabic/English formatting preservation", "main": "src/api.js", "bin": {"arabic-pdf2word-pro-v2": "./src/cli.js"}, "scripts": {"start": "node src/api.js", "cli": "node src/cli.js", "dev": "nodemon src/api.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "format": "prettier --write src/ tests/", "build": "npm run lint && npm run test", "docker:build": "docker build -t arabic-pdf2word-pro-v2 .", "docker:run": "docker-compose up", "prepare": "husky install"}, "keywords": ["pdf", "docx", "arabic", "converter", "ocr", "enterprise", "cli", "api", "rtl", "bidi"], "author": "Arabic PDF2Word Pro Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"commander": "^11.1.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "pdf-lib": "^1.17.1", "pdf2pic": "^3.1.1", "pdfjs-dist": "^4.0.379", "canvas": "^2.11.2", "sharp": "^0.33.1", "tesseract.js": "^5.0.4", "docx": "^8.5.0", "officegen": "^0.6.5", "winston": "^3.11.0", "config": "^3.3.9", "joi": "^17.11.0", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.1", "fs-extra": "^11.2.0", "archiver": "^6.0.1", "mime-types": "^2.1.35", "progress": "^2.0.3", "chalk": "^4.1.2", "ora": "^5.4.1", "inquirer": "^8.2.6"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "nodemon": "^3.0.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@types/jest": "^29.5.8"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/config/**", "!**/node_modules/**"], "coverageThreshold": {"global": {"branches": 95, "functions": 95, "lines": 95, "statements": 95}}}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "repository": {"type": "git", "url": "https://github.com/your-org/arabic-pdf2word-pro-v2.git"}, "bugs": {"url": "https://github.com/your-org/arabic-pdf2word-pro-v2/issues"}, "homepage": "https://github.com/your-org/arabic-pdf2word-pro-v2#readme"}