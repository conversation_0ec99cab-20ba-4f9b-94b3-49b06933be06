{"name": "plagiarism-checker-app", "version": "1.0.0", "description": "تطبيق فحص الاستلال باستخدام Node.js و Copyleaks API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["plagiarism", "checker", "copyleaks", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "joi": "^17.9.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1"}}