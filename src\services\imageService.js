const fs = require('fs-extra');
const path = require('path');
const sharp = require('sharp');
const { fromPath } = require('pdf2pic');
const winston = require('winston');
const config = require('config');

class ImageService {
  constructor() {
    this.logger = winston.createLogger({
      level: config.get('logging.level'),
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: config.get('logging.file.filename') })
      ]
    });
  }

  /**
   * Convert PDF pages to images
   * @param {string} pdfPath - Path to PDF file
   * @param {Object} options - Conversion options
   * @returns {Promise<Array>} Array of image buffers
   */
  async convertPDFToImages(pdfPath, options = {}) {
    try {
      this.logger.info(`Converting PDF to images: ${pdfPath}`);
      
      const dpi = options.dpi || config.get('ocr.dpi');
      const format = options.format || config.get('ocr.imageFormat');
      
      const convert = fromPath(pdfPath, {
        density: dpi,
        saveFilename: "page",
        savePath: config.get('performance.tempDir'),
        format: format,
        width: options.width,
        height: options.height,
        quality: 100
      });

      // Get total page count first
      const pageCount = await this.getPDFPageCount(pdfPath);
      const images = [];

      for (let i = 1; i <= pageCount; i++) {
        try {
          const result = await convert(i, { responseType: "buffer" });
          
          if (result.buffer) {
            images.push(result.buffer);
            this.logger.debug(`Converted page ${i}/${pageCount}`);
          } else {
            this.logger.warn(`Failed to convert page ${i}`);
            images.push(null);
          }
        } catch (pageError) {
          this.logger.warn(`Page ${i} conversion failed: ${pageError.message}`);
          images.push(null);
        }
      }

      this.logger.info(`PDF conversion completed: ${images.filter(img => img !== null).length}/${pageCount} pages`);
      return images.filter(img => img !== null);

    } catch (error) {
      this.logger.error(`PDF to image conversion failed: ${error.message}`);
      throw new Error(`Failed to convert PDF to images: ${error.message}`);
    }
  }

  /**
   * Get PDF page count
   */
  async getPDFPageCount(pdfPath) {
    try {
      const { PDFDocument } = require('pdf-lib');
      const pdfBuffer = await fs.readFile(pdfPath);
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      return pdfDoc.getPageCount();
    } catch (error) {
      this.logger.warn(`Could not get page count: ${error.message}`);
      return 1; // Fallback to single page
    }
  }

  /**
   * Preprocess image for OCR
   * @param {Buffer} imageBuffer - Input image buffer
   * @param {Object} options - Processing options
   * @returns {Promise<Buffer>} Processed image buffer
   */
  async preprocessForOCR(imageBuffer, options = {}) {
    try {
      this.logger.debug('Starting image preprocessing for OCR');
      
      let image = sharp(imageBuffer);
      const metadata = await image.metadata();
      
      // Apply preprocessing pipeline
      image = await this.applyPreprocessingPipeline(image, metadata, options);
      
      // Convert to format suitable for OCR
      const processedBuffer = await image
        .png()
        .toBuffer();
      
      this.logger.debug('Image preprocessing completed');
      return processedBuffer;

    } catch (error) {
      this.logger.warn(`Image preprocessing failed: ${error.message}`);
      return imageBuffer; // Return original if preprocessing fails
    }
  }

  /**
   * Apply comprehensive preprocessing pipeline
   */
  async applyPreprocessingPipeline(image, metadata, options = {}) {
    const preprocessing = config.get('ocr.preprocessing');
    const targetDPI = options.dpi || config.get('ocr.dpi');
    
    // Step 1: Resize to target DPI if needed
    if (metadata.density && metadata.density < targetDPI) {
      const scale = targetDPI / metadata.density;
      image = image.resize(
        Math.round(metadata.width * scale),
        Math.round(metadata.height * scale),
        { kernel: sharp.kernel.lanczos3 }
      );
      this.logger.debug(`Resized image to ${targetDPI} DPI`);
    }

    // Step 2: Convert to grayscale
    image = image.greyscale();

    // Step 3: Deskew if enabled
    if (preprocessing.deskew) {
      image = await this.deskewImage(image);
    }

    // Step 4: Denoise if enabled
    if (preprocessing.denoise) {
      image = image.median(3); // Remove salt and pepper noise
      this.logger.debug('Applied denoising');
    }

    // Step 5: Enhance contrast
    if (preprocessing.adaptiveThreshold) {
      image = await this.enhanceContrast(image);
    }

    // Step 6: Sharpen if enabled
    if (preprocessing.sharpen) {
      image = image.sharpen({
        sigma: 1,
        flat: 1,
        jagged: 2
      });
      this.logger.debug('Applied sharpening');
    }

    // Step 7: Final cleanup
    image = await this.finalCleanup(image);

    return image;
  }

  /**
   * Deskew image to correct rotation
   */
  async deskewImage(image) {
    try {
      // Simple deskewing using edge detection
      // In a production environment, you might want to use more sophisticated algorithms
      
      // Get image stats to detect if rotation is needed
      const { info } = await image.stats();
      
      // This is a simplified implementation
      // Real deskewing would involve:
      // 1. Edge detection
      // 2. Hough transform to find dominant lines
      // 3. Calculate rotation angle
      // 4. Rotate image accordingly
      
      this.logger.debug('Deskewing applied (simplified)');
      return image;
      
    } catch (error) {
      this.logger.warn(`Deskewing failed: ${error.message}`);
      return image;
    }
  }

  /**
   * Enhance image contrast
   */
  async enhanceContrast(image) {
    try {
      // Apply histogram equalization
      image = image.normalize();
      
      // Apply gamma correction for better contrast
      image = image.gamma(1.2);
      
      // Apply adaptive threshold-like effect
      image = image.linear(1.2, -(128 * 0.2));
      
      this.logger.debug('Contrast enhancement applied');
      return image;
      
    } catch (error) {
      this.logger.warn(`Contrast enhancement failed: ${error.message}`);
      return image;
    }
  }

  /**
   * Final cleanup operations
   */
  async finalCleanup(image) {
    try {
      // Remove small artifacts
      image = image.median(1);
      
      // Ensure proper bit depth
      image = image.png({ quality: 100, compressionLevel: 0 });
      
      this.logger.debug('Final cleanup applied');
      return image;
      
    } catch (error) {
      this.logger.warn(`Final cleanup failed: ${error.message}`);
      return image;
    }
  }

  /**
   * Analyze image quality for OCR suitability
   * @param {Buffer} imageBuffer - Image buffer to analyze
   * @returns {Promise<Object>} Quality analysis results
   */
  async analyzeImageQuality(imageBuffer) {
    try {
      const image = sharp(imageBuffer);
      const metadata = await image.metadata();
      const stats = await image.stats();
      
      // Calculate quality metrics
      const quality = {
        resolution: {
          width: metadata.width,
          height: metadata.height,
          density: metadata.density || 72,
          isHighRes: (metadata.density || 72) >= 200
        },
        contrast: this.calculateContrast(stats),
        sharpness: await this.calculateSharpness(image),
        noise: await this.calculateNoise(image),
        overall: 0
      };

      // Calculate overall quality score
      quality.overall = this.calculateOverallQuality(quality);
      
      // Provide recommendations
      quality.recommendations = this.generateQualityRecommendations(quality);
      
      return quality;
      
    } catch (error) {
      this.logger.warn(`Image quality analysis failed: ${error.message}`);
      return {
        overall: 0.5,
        recommendations: ['Could not analyze image quality'],
        error: error.message
      };
    }
  }

  /**
   * Calculate image contrast
   */
  calculateContrast(stats) {
    try {
      const channels = stats.channels;
      if (!channels || channels.length === 0) return 0.5;
      
      // Use the first channel (grayscale or red channel)
      const channel = channels[0];
      const range = channel.max - channel.min;
      const contrast = range / 255; // Normalize to 0-1
      
      return Math.min(contrast, 1);
      
    } catch (error) {
      return 0.5;
    }
  }

  /**
   * Calculate image sharpness using Laplacian variance
   */
  async calculateSharpness(image) {
    try {
      // Apply Laplacian filter to detect edges
      const laplacian = await image
        .clone()
        .convolve({
          width: 3,
          height: 3,
          kernel: [0, -1, 0, -1, 4, -1, 0, -1, 0]
        })
        .stats();
      
      // Higher variance indicates sharper image
      const variance = laplacian.channels[0].stdev;
      return Math.min(variance / 50, 1); // Normalize
      
    } catch (error) {
      return 0.5;
    }
  }

  /**
   * Calculate noise level
   */
  async calculateNoise(image) {
    try {
      // Apply median filter and compare with original
      const original = await image.clone().raw().toBuffer();
      const filtered = await image
        .clone()
        .median(3)
        .raw()
        .toBuffer();
      
      // Calculate difference (noise estimation)
      let diff = 0;
      for (let i = 0; i < Math.min(original.length, filtered.length); i++) {
        diff += Math.abs(original[i] - filtered[i]);
      }
      
      const avgDiff = diff / original.length;
      return Math.max(0, 1 - (avgDiff / 50)); // Invert so higher = less noise
      
    } catch (error) {
      return 0.5;
    }
  }

  /**
   * Calculate overall quality score
   */
  calculateOverallQuality(quality) {
    const weights = {
      resolution: 0.3,
      contrast: 0.3,
      sharpness: 0.25,
      noise: 0.15
    };

    let score = 0;
    score += (quality.resolution.isHighRes ? 1 : 0.5) * weights.resolution;
    score += quality.contrast * weights.contrast;
    score += quality.sharpness * weights.sharpness;
    score += quality.noise * weights.noise;

    return Math.min(score, 1);
  }

  /**
   * Generate quality improvement recommendations
   */
  generateQualityRecommendations(quality) {
    const recommendations = [];

    if (!quality.resolution.isHighRes) {
      recommendations.push('Increase image resolution to at least 200 DPI');
    }

    if (quality.contrast < 0.6) {
      recommendations.push('Improve image contrast');
    }

    if (quality.sharpness < 0.5) {
      recommendations.push('Reduce image blur or improve focus');
    }

    if (quality.noise < 0.7) {
      recommendations.push('Reduce image noise');
    }

    if (quality.overall > 0.8) {
      recommendations.push('Image quality is excellent for OCR');
    } else if (quality.overall > 0.6) {
      recommendations.push('Image quality is good for OCR');
    } else {
      recommendations.push('Image quality may affect OCR accuracy');
    }

    return recommendations;
  }

  /**
   * Create optimized image for specific OCR engine
   * @param {Buffer} imageBuffer - Input image
   * @param {string} engine - OCR engine ('tesseract', etc.)
   * @param {Object} options - Optimization options
   * @returns {Promise<Buffer>} Optimized image
   */
  async optimizeForOCR(imageBuffer, engine = 'tesseract', options = {}) {
    try {
      this.logger.debug(`Optimizing image for ${engine} OCR engine`);
      
      let image = sharp(imageBuffer);
      
      switch (engine.toLowerCase()) {
        case 'tesseract':
          image = await this.optimizeForTesseract(image, options);
          break;
        default:
          image = await this.applyGenericOptimization(image, options);
      }

      const optimizedBuffer = await image.png().toBuffer();
      this.logger.debug('Image optimization completed');
      
      return optimizedBuffer;
      
    } catch (error) {
      this.logger.warn(`Image optimization failed: ${error.message}`);
      return imageBuffer;
    }
  }

  /**
   * Optimize image specifically for Tesseract
   */
  async optimizeForTesseract(image, options = {}) {
    // Tesseract works best with:
    // - High contrast black and white images
    // - 300+ DPI
    // - Clean, noise-free images
    
    const metadata = await image.metadata();
    
    // Ensure minimum size
    if (metadata.width < 1000) {
      const scale = 1000 / metadata.width;
      image = image.resize(
        Math.round(metadata.width * scale),
        Math.round(metadata.height * scale)
      );
    }

    // Convert to grayscale
    image = image.greyscale();
    
    // Apply strong contrast enhancement
    image = image.normalize().gamma(0.8);
    
    // Apply threshold to create clean black and white
    image = image.threshold(128);
    
    return image;
  }

  /**
   * Apply generic OCR optimization
   */
  async applyGenericOptimization(image, options = {}) {
    // Generic optimization for any OCR engine
    image = image
      .greyscale()
      .normalize()
      .sharpen();
    
    return image;
  }

  /**
   * Batch process multiple images
   * @param {Array} imageBuffers - Array of image buffers
   * @param {Function} processor - Processing function
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} Processed images
   */
  async batchProcess(imageBuffers, processor, options = {}) {
    const results = [];
    const concurrency = options.concurrency || 3;
    
    this.logger.info(`Starting batch processing of ${imageBuffers.length} images`);
    
    // Process in batches to avoid memory issues
    for (let i = 0; i < imageBuffers.length; i += concurrency) {
      const batch = imageBuffers.slice(i, i + concurrency);
      const batchPromises = batch.map(async (buffer, index) => {
        try {
          if (!buffer) return null;
          return await processor(buffer, { ...options, index: i + index });
        } catch (error) {
          this.logger.warn(`Batch item ${i + index} failed: ${error.message}`);
          return null;
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      this.logger.debug(`Processed batch ${Math.floor(i / concurrency) + 1}/${Math.ceil(imageBuffers.length / concurrency)}`);
    }
    
    this.logger.info(`Batch processing completed: ${results.filter(r => r !== null).length}/${imageBuffers.length} successful`);
    return results;
  }

  /**
   * Save image to file
   * @param {Buffer} imageBuffer - Image buffer
   * @param {string} outputPath - Output file path
   * @param {Object} options - Save options
   */
  async saveImage(imageBuffer, outputPath, options = {}) {
    try {
      await fs.ensureDir(path.dirname(outputPath));
      
      let image = sharp(imageBuffer);
      
      const ext = path.extname(outputPath).toLowerCase();
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          image = image.jpeg({ quality: options.quality || 95 });
          break;
        case '.png':
          image = image.png({ quality: options.quality || 100 });
          break;
        case '.webp':
          image = image.webp({ quality: options.quality || 95 });
          break;
        default:
          image = image.png();
      }
      
      await image.toFile(outputPath);
      this.logger.debug(`Image saved to: ${outputPath}`);
      
    } catch (error) {
      this.logger.error(`Failed to save image: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ImageService;
