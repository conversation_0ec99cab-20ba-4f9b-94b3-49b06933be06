{"app": {"name": "Arabic PDF2Word Pro v2", "version": "2.0.0", "port": 3000, "host": "0.0.0.0", "env": "development"}, "pdf": {"maxFileSize": "50MB", "supportedFormats": ["pdf"], "extractionMethods": ["text", "ocr", "auto"], "defaultMethod": "auto", "textExtractionThreshold": 0.99, "preserveFormatting": true}, "ocr": {"engine": "tesseract", "languages": ["ara", "eng"], "defaultLang": "ara+eng", "psm": [3, 6, 8, 11], "oem": [1, 3], "dpi": 300, "imageFormat": "png", "preprocessing": {"denoise": true, "deskew": true, "adaptiveThreshold": true, "sharpen": true}, "fallbackStrategies": [{"psm": 6, "oem": 1, "dpi": 300}, {"psm": 8, "oem": 3, "dpi": 400}, {"psm": 11, "oem": 1, "dpi": 600}]}, "docx": {"preserveStyles": true, "preserveImages": true, "preserveTables": true, "preserveFormatting": true, "bidiSupport": true, "rtlSupport": true, "defaultFont": {"arabic": "Traditional Arabic", "english": "Times New Roman"}, "defaultFontSize": 12, "pageMargins": {"top": 720, "right": 720, "bottom": 720, "left": 720}}, "logging": {"level": "info", "format": "json", "file": {"enabled": true, "filename": "logs/app.log", "maxsize": "10MB", "maxFiles": 5}, "console": {"enabled": true, "colorize": true}}, "performance": {"maxConcurrentJobs": 5, "timeoutMs": 300000, "memoryLimitMB": 2048, "tempDir": "./temp", "cleanupInterval": 3600000}, "quality": {"minAccuracy": 0.95, "minSuccessRate": 0.9, "maxProcessingTime": 1000, "enableMetrics": true, "enableMonitoring": true}, "api": {"rateLimit": {"windowMs": 900000, "max": 100}, "cors": {"origin": "*", "methods": ["GET", "POST", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization"]}, "upload": {"dest": "./uploads", "maxFileSize": "50MB", "allowedMimeTypes": ["application/pdf"]}}}